#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
月度工资异动处理系统 - 主应用入口

本文件是系统的主入口，负责启动应用程序和初始化各个模块。

功能:
1. 应用程序初始化
2. 异常处理和日志记录
3. 系统环境检查
4. 主窗口启动
"""

import sys
import os
import traceback
from pathlib import Path

from src.modules.system_config.config_manager import ConfigManager
from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.gui.prototype.prototype_main_window import PrototypeMainWindow

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置应用程序信息
APP_NAME = "月度工资异动处理系统"
APP_VERSION = "2.0.0-refactored"
APP_AUTHOR = "月度工资异动处理系统开发团队"

def setup_environment():
    """设置应用程序环境"""
    try:
        # 创建必要的目录
        directories = [
            'logs',
            'data',
            'output',
            'temp',
            'backup'
        ]
        
        for directory in directories:
            dir_path = project_root / directory
            dir_path.mkdir(exist_ok=True)
        
        # 设置环境变量
        os.environ['APP_NAME'] = APP_NAME
        os.environ['APP_VERSION'] = APP_VERSION
        os.environ['APP_ROOT'] = str(project_root)
        
        return True
        
    except Exception as e:
        print(f"环境设置失败: {e}")
        return False

def check_dependencies():
    """检查依赖项"""
    try:
        # 检查Python版本
        if sys.version_info < (3, 8):
            print("错误: 需要Python 3.8或更高版本")
            return False
        
        # 检查必要的包
        required_packages = [
            'PyQt5',
            'pandas',
            'openpyxl',
            'python-docx',
            'sqlite3'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                if package == 'sqlite3':
                    import sqlite3
                elif package == 'PyQt5':
                    import PyQt5
                elif package == 'pandas':
                    import pandas
                elif package == 'openpyxl':
                    import openpyxl
                elif package == 'python-docx':
                    import docx
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"错误: 缺少以下必要的包: {', '.join(missing_packages)}")
            print("请运行: pip install -r requirements.txt")
            return False
        
        return True
        
    except Exception as e:
        print(f"依赖检查失败: {e}")
        return False

def setup_exception_handler():
    """设置全局异常处理器"""
    def handle_exception(exc_type, exc_value, exc_traceback):
        """全局异常处理函数"""
        if issubclass(exc_type, KeyboardInterrupt):
            # 允许Ctrl+C正常退出
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        # 记录异常到日志文件
        error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        
        # 🔧 [P0-修复] 使用loguru记录异常，统一日志格式
        try:
            from src.utils.log_config import setup_logger
            logger = setup_logger("global_exception_handler")
            logger.critical(f"🔧 [P0-修复] 全局未捕获异常: {exc_type.__name__}: {exc_value}")
            logger.critical(f"🔧 [P0-修复] 异常详情:\n{error_msg}")
        except Exception:
            # 如果loguru不可用，回退到直接文件写入
            log_file = project_root / 'logs' / 'error.log'
            try:
                with open(log_file, 'a', encoding='utf-8') as f:
                    from datetime import datetime
                    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    f.write(f"\n[{timestamp}] 未捕获的异常:\n{error_msg}\n")
            except Exception:
                pass  # 如果无法写入日志，忽略错误
        
        # 显示错误对话框
        try:
            from PyQt5.QtWidgets import QMessageBox, QApplication
            
            if QApplication.instance():
                msg_box = QMessageBox()
                msg_box.setIcon(QMessageBox.Critical)
                msg_box.setWindowTitle("系统错误")
                msg_box.setText("系统发生未处理的错误，程序将尝试继续运行")
                msg_box.setDetailedText(error_msg)
                msg_box.setStandardButtons(QMessageBox.Ok)
                msg_box.exec_()
        except Exception:
            # 如果GUI不可用，打印到控制台
            print(f"系统错误: {error_msg}")
    
    # 设置异常处理器
    sys.excepthook = handle_exception

def setup_qt_exception_handling(app):
    """设置PyQt专用异常处理机制"""
    try:
        from src.utils.log_config import setup_logger
        logger = setup_logger("qt_exception_handler")
        
        # 保存原始的异常处理器
        original_excepthook = sys.excepthook
        
        def qt_aware_excepthook(exc_type, exc_value, exc_traceback):
            """🔧 [P0-CRITICAL] 增强的PyQt异常处理器"""
            try:
                # 🔧 [P0-CRITICAL] 详细记录异常信息
                error_details = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
                logger.critical(f"🔧 [P0-CRITICAL] PyQt异常捕获: {exc_type.__name__}: {exc_value}")
                logger.critical(f"🔧 [P0-CRITICAL] 异常堆栈:\n{error_details}")
                
                # 🔧 [P0-CRITICAL] 特殊处理Qt线程相关错误
                error_message = str(exc_value)
                is_qt_thread_error = any(keyword in error_message.lower() for keyword in [
                    'qbasictimer', 'qobject', 'thread', 'timer', 'metaobject', 'signal'
                ])
                
                if is_qt_thread_error:
                    logger.critical(f"🔧 [P0-CRITICAL] 检测到Qt线程安全相关错误: {error_message}")
                
                # 🔧 [P0-CRITICAL] 安全的错误对话框显示
                from PyQt5.QtWidgets import QMessageBox, QApplication
                from PyQt5.QtCore import QMetaObject, Qt, QCoreApplication
                
                app = QApplication.instance()
                if app and not isinstance(exc_value, KeyboardInterrupt):
                    try:
                        # 🔧 [P0-CRITICAL] 使用线程安全的方式显示错误对话框
                        def safe_show_error_dialog():
                            """线程安全的错误对话框显示"""
                            try:
                                from src.utils.thread_safe_timer import ThreadSafeTimer
                                
                                # 确保在主线程中显示
                                if not ThreadSafeTimer.is_main_thread():
                                    logger.warning("🔧 [P0-CRITICAL] 异常处理器在非主线程，跳过对话框显示")
                                    return
                                
                                msg = QMessageBox()
                                msg.setIcon(QMessageBox.Critical)
                                msg.setWindowTitle("系统异常")
                                
                                if is_qt_thread_error:
                                    msg.setText("检测到Qt线程安全问题，程序已自动修复")
                                    msg.setInformativeText("请重新尝试操作。如问题持续，请重启程序。")
                                else:
                                    msg.setText("程序发生异常，已自动处理")
                                    msg.setInformativeText("程序将继续运行。如问题持续，请重启程序。")
                                
                                msg.setDetailedText(f"异常类型: {exc_type.__name__}\n异常信息: {exc_value}\n\n{error_details}")
                                msg.setStandardButtons(QMessageBox.Ok)
                                msg.exec_()
                                
                            except Exception as dialog_error:
                                logger.error(f"🔧 [P0-CRITICAL] 错误对话框显示失败: {dialog_error}")
                        
                        # 🔧 [P0-CRITICAL] 强制在主线程中显示对话框
                        QMetaObject.invokeMethod(
                            app,
                            safe_show_error_dialog,
                            Qt.QueuedConnection
                        )
                        
                    except Exception as dialog_setup_error:
                        logger.error(f"🔧 [P0-CRITICAL] 错误对话框设置失败: {dialog_setup_error}")
                
                # 🔧 [P0-CRITICAL] 处理特定类型的错误
                if is_qt_thread_error:
                    # 对于Qt线程错误，尝试清理和重置状态
                    try:
                        QCoreApplication.processEvents()
                        logger.info(f"🔧 [P0-CRITICAL] Qt事件队列已清理")
                    except Exception as cleanup_error:
                        logger.error(f"🔧 [P0-CRITICAL] Qt状态清理失败: {cleanup_error}")
                
            except Exception as handler_error:
                print(f"🔧 [P0-CRITICAL] Qt异常处理器内部错误: {handler_error}")
                # 最后的回退方案
                print(f"原始异常: {exc_type.__name__}: {exc_value}")
            
            # 🔧 [P0-CRITICAL] 不调用原始异常处理器，防止程序退出
            # 对于严重错误，记录但不终止程序
            logger.info(f"🔧 [P0-CRITICAL] 异常已处理，程序继续运行")
        
        # 应用PyQt专用异常处理器
        sys.excepthook = qt_aware_excepthook
        
        # 🔧 [P0-CRITICAL] 创建Qt事件过滤器捕获更多底层错误
        class QtEventFilter(QObject):
            """Qt事件过滤器，捕获Qt底层错误"""
            
            def eventFilter(self, obj, event):
                try:
                    return super().eventFilter(obj, event)
                except Exception as filter_error:
                    logger.error(f"🔧 [P0-CRITICAL] Qt事件过滤器捕获异常: {filter_error}", exc_info=True)
                    # 捕获异常但继续处理事件
                    return False
        
        # 安装全局事件过滤器
        try:
            from PyQt5.QtCore import QObject
            qt_filter = QtEventFilter()
            # 注意：这里暂时注释掉全局事件过滤器，避免性能问题
            # app.installEventFilter(qt_filter)  
            logger.info("🔧 [P0-CRITICAL] Qt事件过滤器已准备（按需启用）")
        except Exception as filter_setup_error:
            logger.warning(f"🔧 [P0-CRITICAL] Qt事件过滤器设置失败: {filter_setup_error}")
        
        logger.info("🔧 [P0-CRITICAL] 增强的PyQt异常处理机制已启用")
        
    except Exception as e:
        print(f"设置PyQt异常处理失败: {e}")

def create_application():
    """创建QApplication实例"""
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QIcon, QFont
        
        # 🔧 [P0-CRITICAL修复] 全面解决Qt元对象类型注册问题
        try:
            from PyQt5.QtCore import Qt, qRegisterMetaType, QModelIndex, QItemSelection, QItemSelectionRange
            from PyQt5.QtWidgets import QTableWidgetItem

            # 🔧 [P0-CRITICAL] 预先加载Qt枚举类型避免运行时错误
            try:
                _ = Qt.Orientation.Horizontal
                _ = Qt.Orientation.Vertical
                _ = Qt.SortOrder.AscendingOrder
                _ = Qt.SortOrder.DescendingOrder
                _ = Qt.AlignmentFlag.AlignLeft
                _ = Qt.AlignmentFlag.AlignCenter
                _ = Qt.ItemDataRole.DisplayRole
                _ = Qt.ItemDataRole.EditRole
                print("[成功] Qt枚举类型预加载完成")
            except Exception as enum_error:
                print(f"[警告] Qt枚举类型预加载失败: {enum_error}")

            # 🔧 [P0-CRITICAL] 注册所有必要的Qt元类型
            registered_types = []
            critical_types = [
                # 基本枚举类型（信号连接必需）
                ("Qt::Orientation", Qt.Orientation),
                ("Qt::SortOrder", Qt.SortOrder), 
                ("Qt::AlignmentFlag", Qt.AlignmentFlag),
                
                # 容器类型（表格操作必需）
                ("QList<int>", None),
                ("QVector<int>", None),
                ("QList<QModelIndex>", None),
                
                # 表格相关类型
                ("QModelIndex", QModelIndex),
                ("QItemSelection", QItemSelection),
                ("QItemSelectionRange", QItemSelectionRange),
                ("QTableWidgetItem", QTableWidgetItem),
                
                # 几何类型（UI布局必需）
                ("QSize", None),
                ("QRect", None),
                ("QPoint", None),
            ]
            
            for type_name, type_obj in critical_types:
                try:
                    if type_obj is not None:
                        # 对于具体类型，先确保类型已加载
                        if hasattr(type_obj, '__name__'):
                            type_id = qRegisterMetaType(type_name)
                        else:
                            # 对于枚举类型，直接注册
                            type_id = qRegisterMetaType(type_name)
                    else:
                        # 对于容器类型，直接尝试注册
                        type_id = qRegisterMetaType(type_name)
                    
                    if type_id != -1:
                        registered_types.append(f"{type_name}({type_id})")
                    else:
                        print(f"[警告] {type_name} 注册失败，返回ID: {type_id}")
                        
                except Exception as reg_error:
                    print(f"[警告] 注册 {type_name} 失败: {reg_error}")
                    continue
            
            print(f"[成功] Qt类型注册完成: {len(registered_types)}个类型")
            if registered_types:
                print(f"[详细] 已注册类型: {', '.join(registered_types[:5])}..." if len(registered_types) > 5 else f"[详细] 已注册类型: {', '.join(registered_types)}")
                
        except Exception as type_error:
            print(f"[严重] Qt类型注册完全失败: {type_error}")
            print("[回退] 使用DirectConnection连接方式")
        
        # 设置高DPI支持 - 必须在QApplication创建之前设置
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # 创建应用程序实例
        app = QApplication(sys.argv)
        
        # 设置应用程序属性
        app.setApplicationName(APP_NAME)
        app.setApplicationVersion(APP_VERSION)
        app.setOrganizationName(APP_AUTHOR)
        
        # 设置应用程序图标
        icon_path = project_root / 'resources' / 'icons' / 'app_icon.png'
        if icon_path.exists():
            app.setWindowIcon(QIcon(str(icon_path)))
        
        # 设置全局字体
        font = QFont("Microsoft YaHei", 9)
        app.setFont(font)
        
        # 设置样式
        app.setStyle('Fusion')
        
        # 🔧 [P0-修复] 应用Material Design样式系统
        try:
            from src.gui.style_manager import StyleManager
            style_manager = StyleManager.get_instance()
            
            # 应用全局样式
            if style_manager.apply_global_style(app):
                print("[成功] Material Design全局样式应用成功")
            else:
                print("Material Design全局样式应用失败，使用默认样式")
                
            # 启用样式热重载（开发环境）
            style_manager.enable_hot_reload(True)
            
        except Exception as style_error:
            print(f"[警告] 样式系统初始化失败: {style_error}")
            print("[信息] 继续使用默认Fusion样式")
        
        # 🔧 [P0-修复] 设置PyQt专用异常处理
        setup_qt_exception_handling(app)
        
        return app
        
    except Exception as e:
        print(f"创建应用程序失败: {e}")
        return None

def create_main_window(config_manager, db_manager, dynamic_table_manager):
    """创建主窗口"""
    try:
        # 重构后，我们统一使用 PrototypeMainWindow
        main_window = PrototypeMainWindow(
            config_manager=config_manager,
            db_manager=db_manager,
            dynamic_table_manager=dynamic_table_manager
        )
        
        main_window.show()
        return main_window
        
    except Exception as e:
        print(f"创建主窗口失败: {e}")
        traceback.print_exc()
        return None

def setup_app_logging():
    """设置日志系统"""
    try:
        from src.utils.log_config import setup_logger
        logger = setup_logger(__name__)
        logger.info(f"{APP_NAME} v{APP_VERSION} 启动")
        return logger
    except Exception as e:
        print(f"日志系统初始化失败: {e}")
        return None

def show_splash_screen(app):
    """显示启动画面"""
    try:
        from PyQt5.QtWidgets import QSplashScreen, QLabel
        from PyQt5.QtCore import Qt, QTimer
        from PyQt5.QtGui import QPixmap, QFont
        
        # 创建启动画面
        splash_pix = QPixmap(400, 300)
        splash_pix.fill(Qt.white)
        
        splash = QSplashScreen(splash_pix, Qt.WindowStaysOnTopHint)
        
        # 添加启动信息
        splash.showMessage(
            f"{APP_NAME}\n版本 {APP_VERSION}\n正在启动...",
            Qt.AlignCenter | Qt.AlignBottom,
            Qt.black
        )
        
        splash.show()
        app.processEvents()
        
        return splash
        
    except Exception as e:
        print(f"启动画面创建失败: {e}")
        return None

def main():
    """主函数"""
    try:
        from PyQt5.QtCore import Qt, QTimer
        
        print(f"正在启动 {APP_NAME} v{APP_VERSION}...")
        
        # 1. 设置环境
        if not setup_environment():
            sys.exit(1)
        
        # 2. 检查依赖
        if not check_dependencies():
            sys.exit(1)
        
        # 3. 设置全局异常处理器
        setup_exception_handler()
        
        # 4. 创建应用程序
        app = create_application()
        if not app:
            sys.exit(1)
            
        # 5. 设置日志
        logger = setup_app_logging()
        if not logger:
            sys.exit(1)

        # 6. 初始化核心管理器
        logger.info("初始化核心管理器...")
        try:
            config_manager = ConfigManager()
            db_manager = DatabaseManager(config_manager=config_manager)
            dynamic_table_manager = DynamicTableManager(db_manager=db_manager)
            logger.info("核心管理器初始化完成。")
        except Exception as e:
            logger.error(f"核心管理器初始化失败: {e}")
            traceback.print_exc()
            sys.exit(1)
            
        # 7. 显示启动画面 (可选)
        # splash = show_splash_screen(app)
        
        # 8. 创建主窗口
        main_window = create_main_window(
            config_manager=config_manager,
            db_manager=db_manager,
            dynamic_table_manager=dynamic_table_manager
        )
        if not main_window:
            sys.exit(1)
            
        # 关闭启动画面
        # if splash:
        #     splash.finish(main_window)
            
        logger.info("应用程序启动成功")
        print(f"{APP_NAME} 启动成功!")
        
        # 9. 运行应用程序
        exit_code = app.exec_()
        logger.info("应用程序正常退出")
        print("应用程序已退出")
        sys.exit(exit_code)

    except Exception as e:
        # 记录任何在主函数中发生的未捕获异常
        error_msg = traceback.format_exc()
        try:
            # 尝试写入日志
            log_file = project_root / 'logs' / 'error.log'
            with open(log_file, 'a', encoding='utf-8') as f:
                from datetime import datetime
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                f.write(f"\n[{timestamp}] 主函数发生严重错误:\n{error_msg}\n")
        except:
            pass # 忽略日志写入错误
        
        print(f"主函数发生严重错误: {e}")
        sys.exit(1)

def run_tests():
    """运行测试"""
    try:
        import pytest
        print("正在运行测试...")
        result = pytest.main(['-v', 'test/'])
        return result == 0
    except ImportError:
        print("pytest未安装，跳过测试")
        return True
    except Exception as e:
        print(f"测试运行失败: {e}")
        return False

def show_version():
    """显示版本信息"""
    print(f"""
{APP_NAME} v{APP_VERSION}

开发团队: {APP_AUTHOR}
Python版本: {sys.version}
系统平台: {sys.platform}

项目目录: {project_root}
    """)

def show_help():
    """显示帮助信息"""
    print(f"""
{APP_NAME} v{APP_VERSION}

用法:
    python main.py [选项]

选项:
    -h, --help      显示此帮助信息
    -v, --version   显示版本信息
    --test          运行测试
    --check         检查系统环境和依赖

示例:
    python main.py              # 正常启动程序
    python main.py --test       # 运行测试
    python main.py --check      # 检查环境
    """)

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if '--test' in sys.argv:
            run_tests()
        elif '--version' in sys.argv:
            show_version()
        elif '--help' in sys.argv:
            show_help()
        else:
            main()
    else:
        main() 