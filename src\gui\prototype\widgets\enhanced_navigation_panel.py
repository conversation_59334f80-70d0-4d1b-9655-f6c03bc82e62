"""
PyQt5重构高保真原型 - 增强导航面板

集成智能展开和搜索功能的增强导航面板组件。
基于SmartTreeExpansion和SmartSearchDebounce算法实现智能用户体验。

主要功能:
1. 智能树形展开预测和自动展开
2. 智能防抖搜索优化
3. 导航状态持久化
4. 用户习惯学习和适应
5. 与主工作区域的深度联动
"""

import json
import os
import time
from typing import Dict, List, Optional, Any, Tuple
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLineEdit, QTreeWidget,
    QTreeWidgetItem, QTreeWidgetItemIterator, QPushButton, QLabel, QFrame, QSizePolicy,
    QApplication
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QPropertyAnimation, QEasingCurve, QThread
from PyQt5.QtGui import QFont, QIcon, QColor, QPixmap, QPainter, QBrush, QColor
import logging

from src.utils.log_config import setup_logger
from .smart_tree_expansion import SmartTreeExpansion
from .smart_search_debounce import SmartSearchDebounce

from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.gui.prototype.managers.communication_manager import ComponentCommunicationManager


class NavigationStateManager:
    """
    导航状态管理器
    
    管理导航面板的状态持久化，包括展开状态、搜索历史等。
    """
    
    def __init__(self, state_file: str = "state/user/navigation_state.json"):
        self.state_file = state_file
        self.logger = setup_logger(__name__ + ".NavigationStateManager")
        
        # 状态数据
        self.expanded_paths = set()
        self.selected_path = ""
        self.search_history = []
        self.user_preferences = {}
        
        self._load_state()
    
    def save_expanded_state(self, path: str, expanded: bool):
        """
        保存展开状态
        
        Args:
            path: 节点路径
            expanded: 是否展开
        """
        if expanded:
            self.expanded_paths.add(path)
        else:
            self.expanded_paths.discard(path)
        
        self._save_state()
    
    def save_selected_state(self, path: str):
        """
        保存选择状态
        
        Args:
            path: 选择的路径
        """
        self.selected_path = path
        self._save_state()
    
    def add_search_history(self, query: str):
        """
        添加搜索历史
        
        Args:
            query: 搜索查询
        """
        if query and query not in self.search_history:
            self.search_history.insert(0, query)
            self.search_history = self.search_history[:20]  # 保留最近20条
            self._save_state()
    
    def get_expanded_paths(self) -> set:
        """
        获取展开的路径
        
        Returns:
            展开路径集合
        """
        return self.expanded_paths.copy()
    
    def get_selected_path(self) -> str:
        """
        获取选择的路径
        
        Returns:
            选择的路径
        """
        return self.selected_path
    
    def get_search_history(self) -> List[str]:
        """
        获取搜索历史
        
        Returns:
            搜索历史列表
        """
        return self.search_history.copy()
    
    def _save_state(self):
        """
        保存状态到文件
        """
        try:
            os.makedirs(os.path.dirname(self.state_file), exist_ok=True)
            
            state_data = {
                'expanded_paths': list(self.expanded_paths),
                'selected_path': self.selected_path,
                'search_history': self.search_history,
                'user_preferences': self.user_preferences
            }
            
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            self.logger.error(f"保存导航状态失败: {e}")
    
    def _load_state(self):
        """
        从文件加载状态
        """
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    state_data = json.load(f)
                
                self.expanded_paths = set(state_data.get('expanded_paths', []))
                self.selected_path = state_data.get('selected_path', '')
                self.search_history = state_data.get('search_history', [])
                self.user_preferences = state_data.get('user_preferences', {})
                
                self.logger.info("导航状态加载成功")
            
        except Exception as e:
            self.logger.warning(f"导航状态加载失败: {e}")


class SmartTreeWidget(QTreeWidget):
    """
    智能树形控件
    
    集成智能展开预测功能的树形控件。
    """
    
    # 智能展开信号
    smart_expansion_triggered = pyqtSignal(str, bool)  # (path, expanded)
    path_predicted = pyqtSignal(list)  # (predicted_paths)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__ + ".SmartTreeWidget")
        
        # 路径映射 - 使用item的id作为键
        self.item_path_map = {}  # item_id -> path
        self.path_item_map = {}  # path -> item
        
        # 智能展开算法
        self.smart_expansion = SmartTreeExpansion()
        self.smart_expansion.expansion_suggested.connect(self._apply_smart_expansion)
        
        # 设置样式
        self._setup_styles()
        
        # 连接信号
        self.itemExpanded.connect(self._on_item_expanded)
        self.itemCollapsed.connect(self._on_item_collapsed)
        self.itemSelectionChanged.connect(self._on_selection_changed)
    
    def _setup_styles(self):
        """
        设置树形控件样式
        """
        self.setStyleSheet("""
            QTreeWidget {
                border: none;
                background-color: #fafafa;
                alternate-background-color: #f5f5f5;
                selection-background-color: #e3f2fd;
                outline: none;
            }
            QTreeWidget::item {
                padding: 8px 12px;
                border: none;
                border-bottom: 1px solid #eeeeee;
            }
            QTreeWidget::item:hover {
                background-color: #f0f0f0;
            }
            QTreeWidget::item:selected {
                background-color: #2196F3;
                color: white;
                font-weight: bold;
            }
            QTreeWidget::item:selected:hover {
                background-color: #1976D2;
            }
            QTreeWidget::branch {
                background-color: transparent;
            }
            QTreeWidget::branch:has-siblings:!adjoins-item {
                border-image: none;
                border: none;
            }
            QTreeWidget::branch:has-siblings:adjoins-item {
                border-image: none;
                border: none;
            }
            QTreeWidget::branch:!has-children:!has-siblings:adjoins-item {
                border-image: none;
                border: none;
            }
            QTreeWidget::branch:has-children:!has-siblings:closed,
            QTreeWidget::branch:closed:has-children:has-siblings {
                border-image: none;
                image: url(none);
            }
            QTreeWidget::branch:open:has-children:!has-siblings,
            QTreeWidget::branch:open:has-children:has-siblings {
                border-image: none;
                image: url(none);
            }
        """)
    
    def add_navigation_item(self, parent_path: str, item_text: str, icon_text: str = "") -> str:
        """
        添加导航项
        
        Args:
            parent_path: 父级路径
            item_text: 项目文本
            icon_text: 图标文本
            
        Returns:
            新项目的路径
        """
        # 构建路径
        if parent_path:
            path = f"{parent_path} > {item_text}"
        else:
            path = item_text
        
        # 创建项目
        display_text = f"{icon_text} {item_text}" if icon_text else item_text
        
        if parent_path and parent_path in self.path_item_map:
            parent_item = self.path_item_map[parent_path]
            item = QTreeWidgetItem(parent_item, [display_text])
        else:
            item = QTreeWidgetItem(self, [display_text])
        
        # 设置项目属性
        item.setData(0, Qt.UserRole, path)
        
        # 更新映射
        self.item_path_map[id(item)] = path
        self.path_item_map[path] = item
        
        return path
    
    def expand_path(self, path: str, record_access: bool = True):
        """
        展开指定路径，并确保所有父级节点也被展开

        Args:
            path: 要展开的路径
            record_access: 是否记录访问
        """
        if path in self.path_item_map:
            item = self.path_item_map[path]
            # 验证QTreeWidgetItem是否仍然有效
            try:
                if not item or item.treeWidget() is None:
                    # 如果项目已被删除，从映射中移除
                    self.path_item_map.pop(path, None)
                    if id(item) in self.item_path_map:
                        self.item_path_map.pop(id(item), None)
                    return

                # 首先确保所有父级节点都被展开
                self._expand_parent_nodes(item)

                # 然后展开当前节点
                item.setExpanded(True)

                if record_access:
                    self.smart_expansion.record_access(path)
            except RuntimeError:
                # QTreeWidgetItem已被删除，清理映射
                self.path_item_map.pop(path, None)
                if id(item) in self.item_path_map:
                    self.item_path_map.pop(id(item), None)

    def _expand_parent_nodes(self, item):
        """
        递归展开所有父级节点

        Args:
            item: 目标节点
        """
        parent = item.parent()
        if parent:
            # 递归展开父级的父级
            self._expand_parent_nodes(parent)
            # 展开父级节点
            parent.setExpanded(True)
    
    def collapse_path(self, path: str):
        """
        折叠指定路径
        
        Args:
            path: 要折叠的路径
        """
        if path in self.path_item_map:
            item = self.path_item_map[path]
            # 验证QTreeWidgetItem是否仍然有效
            try:
                if not item or item.treeWidget() is None:
                    # 如果项目已被删除，从映射中移除
                    self.path_item_map.pop(path, None)
                    if id(item) in self.item_path_map:
                        self.item_path_map.pop(id(item), None)
                    return
                
                item.setExpanded(False)
            except RuntimeError:
                # QTreeWidgetItem已被删除，清理映射
                self.path_item_map.pop(path, None)
                if id(item) in self.item_path_map:
                    self.item_path_map.pop(id(item), None)
    
    def select_path(self, path: str, record_access: bool = True):
        """
        选择指定路径
        
        Args:
            path: 要选择的路径
            record_access: 是否记录访问
        """
        if path in self.path_item_map:
            item = self.path_item_map[path]
            # 验证QTreeWidgetItem是否仍然有效
            try:
                if not item or item.treeWidget() is None:
                    # 如果项目已被删除，从映射中移除
                    self.path_item_map.pop(path, None)
                    if id(item) in self.item_path_map:
                        self.item_path_map.pop(id(item), None)
                    return
                
                self.setCurrentItem(item)
                
                if record_access:
                    self.smart_expansion.record_access(path, {'action': 'select'})
            except RuntimeError:
                # QTreeWidgetItem已被删除，清理映射
                self.path_item_map.pop(path, None)
                if id(item) in self.item_path_map:
                    self.item_path_map.pop(id(item), None)
    
    def predict_and_expand(self, context: Dict[str, Any] = None):
        """
        预测并展开路径
        
        Args:
            context: 预测上下文
        """
        predictions = self.smart_expansion.predict_expansion_paths(context)
        
        if predictions:
            self.path_predicted.emit([path for path, _ in predictions])
            
            # 自动展开置信度较高的路径
            for path, confidence in predictions:
                if confidence > 0.6:  # 置信度阈值
                    self.expand_path(path, record_access=False)
    
    def _apply_smart_expansion(self, suggested_paths: List[str]):
        """
        应用智能展开建议
        
        Args:
            suggested_paths: 建议展开的路径列表
        """
        for path in suggested_paths[:3]:  # 限制自动展开数量
            self.expand_path(path, record_access=False)
    
    def _on_item_expanded(self, item):
        """
        处理项目展开事件
        
        Args:
            item: 展开的项目
        """
        if id(item) in self.item_path_map:
            path = self.item_path_map[id(item)]
            self.smart_expansion.record_access(path, {'action': 'expand'})
            self.smart_expansion_triggered.emit(path, True)
    
    def _on_item_collapsed(self, item):
        """
        处理项目折叠事件
        
        Args:
            item: 折叠的项目
        """
        if id(item) in self.item_path_map:
            path = self.item_path_map[id(item)]
            self.smart_expansion_triggered.emit(path, False)
    
    def _on_selection_changed(self):
        """
        处理选择变化事件
        """
        current = self.currentItem()
        if current and id(current) in self.item_path_map:
            path = self.item_path_map[id(current)]
            self.smart_expansion.record_access(path, {'action': 'select'})
    
    def filter_items(self, query: str):
        """
        过滤树形项目
        
        Args:
            query: 过滤查询
        """
        # 实现过滤逻辑
        iterator = QTreeWidgetItemIterator(self)
        while iterator.value():
            item = iterator.value()
            item_text = item.text(0).lower()
            is_match = query.lower() in item_text if query else True
            item.setHidden(not is_match)
            iterator += 1
    
    def clear_mappings(self):
        """
        清除所有项目映射
        
        在重建树形控件时调用，避免保留对已删除项目的引用
        """
        self.path_item_map.clear()
        self.item_path_map.clear()
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息字典
        """
        return {
            'total_items': len(self.item_path_map),
            'expansion_stats': self.smart_expansion.get_statistics()
        }


class EnhancedNavigationPanel(QWidget):
    """
    增强导航面板
    
    集成智能展开和搜索功能的导航面板组件。
    """
    
    # 导航信号
    navigation_changed = pyqtSignal(str, dict)  # (path, context)
    search_performed = pyqtSignal(str, dict)  # (query, context)
    expansion_predicted = pyqtSignal(list)  # (predicted_paths)
    
    def __init__(self, dynamic_table_manager: DynamicTableManager, comm_manager: ComponentCommunicationManager, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # --- Manager Injection ---
        self.dynamic_table_manager = dynamic_table_manager
        self.comm_manager = comm_manager
        
        # 状态管理器
        self.state_manager = NavigationStateManager()
        
        # 智能搜索算法
        self.search_debounce = SmartSearchDebounce()
        
        # UI组件
        self.search_box: Optional[QLineEdit] = None
        self.tree_widget: Optional[SmartTreeWidget] = None
        self.stats_label: Optional[QLabel] = None
        
        # 线程安全的Timer管理
        self.stats_timer: Optional[QTimer] = None
        self._timer_destroyed = False
        
        self._setup_ui()
        self._setup_navigation_data()
        self._setup_connections()
        self._restore_state()
        self._initial_prediction()

        # 标记是否需要自动选择（避免重复执行）
        self._auto_select_pending = True

        # 🔧 [P1-1修复] 线程安全的延迟自动选择最新数据
        # 注意：如果导航树需要刷新，自动选择会在刷新后执行
        # 增加延迟时间确保数据库完全就绪
        from src.utils.thread_safe_timer import ThreadSafeTimer
        ThreadSafeTimer.safe_single_shot(800, self._delayed_auto_select_latest_data)

        self.logger.info("增强导航面板初始化完成")
    
    def _setup_stats_timer_safe(self):
        """线程安全地设置统计信息更新定时器"""
        try:
            # 确保在主线程中创建Timer
            if QThread.currentThread() != QApplication.instance().thread():
                self.logger.warning("🔧 [Timer修复] 非主线程，延迟到主线程创建Timer")
                QTimer.singleShot(0, self._setup_stats_timer_safe)
                return
            
            # 清理可能存在的旧Timer
            self._cleanup_stats_timer()
            
            # 创建新的线程安全Timer
            self.stats_timer = QTimer(self)  # 明确指定parent确保生命周期管理
            self.stats_timer.timeout.connect(self._update_statistics_safe)
            self.stats_timer.start(5000)  # 每5秒更新一次
            self._timer_destroyed = False
            
            self.logger.debug("🔧 [Timer修复] 统计信息定时器已在主线程中安全创建")
            
        except Exception as e:
            self.logger.error(f"🔧 [Timer修复] 创建统计定时器失败: {e}")
    
    def _cleanup_stats_timer(self):
        """安全清理统计定时器"""
        if self.stats_timer and not self._timer_destroyed:
            try:
                if self.stats_timer.isActive():
                    self.stats_timer.stop()
                self.stats_timer.deleteLater()
                self.stats_timer = None
                self._timer_destroyed = True
                self.logger.debug("🔧 [Timer修复] 旧统计定时器已安全清理")
            except Exception as e:
                self.logger.warning(f"🔧 [Timer修复] 清理统计定时器时出现异常: {e}")
    
    def _update_statistics_safe(self):
        """线程安全的统计信息更新"""
        try:
            # 确保在主线程中更新UI
            if QThread.currentThread() != QApplication.instance().thread():
                self.logger.warning("🔧 [Timer修复] 非主线程调用，转移到主线程")
                QTimer.singleShot(0, self._update_statistics_safe)
                return
            
            # 检查组件是否仍然有效
            if self._timer_destroyed or not self.stats_label:
                return
                
            self._update_statistics()
            
        except Exception as e:
            self.logger.error(f"🔧 [Timer修复] 更新统计信息失败: {e}")
    
    def closeEvent(self, event):
        """组件关闭时的清理工作"""
        try:
            self.logger.debug("🔧 [Timer修复] 增强导航面板正在关闭，清理资源")
            self._cleanup_stats_timer()
            super().closeEvent(event)
        except Exception as e:
            self.logger.error(f"🔧 [Timer修复] 关闭清理失败: {e}")
            super().closeEvent(event)
    
    def _setup_ui(self):
        """
        设置用户界面
        """
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(12)
        
        # 标题区域
        title_frame = QFrame()
        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(0, 0, 0, 0)
        
        title_label = QLabel("● 智能导航")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #2196F3;")
        
        # 智能指示器
        self.smart_indicator = QLabel("🤖")
        self.smart_indicator.setToolTip("智能预测已启用")
        self.smart_indicator.setStyleSheet("""
            QLabel {
                background-color: #4CAF50;
                color: white;
                border-radius: 10px;
                padding: 2px 6px;
                font-size: 10px;
            }
        """)
        
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        title_layout.addWidget(self.smart_indicator)
        
        # 搜索区域
        search_frame = QFrame()
        search_layout = QVBoxLayout(search_frame)
        search_layout.setContentsMargins(0, 0, 0, 0)
        search_layout.setSpacing(8)
        
        # 搜索框
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("智能搜索...")
        self.search_edit.setStyleSheet("""
            QLineEdit {
                padding: 10px 15px;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #2196F3;
                background-color: #f8f9fa;
            }
        """)
        
        # 搜索状态指示器
        self.search_status = QLabel("")
        self.search_status.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 11px;
                padding: 2px 5px;
            }
        """)
        
        search_layout.addWidget(self.search_edit)
        search_layout.addWidget(self.search_status)
        
        # 智能树形导航
        self.tree_widget = SmartTreeWidget()
        self.tree_widget.setHeaderHidden(True)
        
        # 统计信息区域
        self.stats_label = QLabel("")
        self.stats_label.setStyleSheet("""
            QLabel {
                color: #888;
                font-size: 10px;
                padding: 5px;
                background-color: #f5f5f5;
                border-radius: 4px;
            }
        """)
        
        # 布局安排
        layout.addWidget(title_frame)
        layout.addWidget(search_frame)
        layout.addWidget(self.tree_widget)
        layout.addWidget(self.stats_label)
    
    def _setup_navigation_data(self):
        """
        设置导航数据
        
        重构后只包含数据导航，功能性导航已移至菜单栏
        """
        # 工资表数据模块（主要数据导航）
        salary_path = self.tree_widget.add_navigation_item("", "工资表", "💰")
        
        # 2025年数据
        year_2025_path = self.tree_widget.add_navigation_item(salary_path, "2025年", "📅")
        
        # 动态加载月份数据
        active_employees_path = self._load_dynamic_salary_data(year_2025_path)
        
        # 2024年数据
        year_2024_path = self.tree_widget.add_navigation_item(salary_path, "2024年", "📅")
        month_12_path = self.tree_widget.add_navigation_item(year_2024_path, "12月", "📊")
        self.tree_widget.add_navigation_item(month_12_path, "全部在职人员", "👥")
        self.tree_widget.add_navigation_item(month_12_path, "离休人员", "🏆")
        self.tree_widget.add_navigation_item(month_12_path, "退休人员", "🌅")
        
        # 异动人员表模块
        change_path = self.tree_widget.add_navigation_item("", "异动人员表", "🔄")
        
        # 2025年异动数据
        change_2025_path = self.tree_widget.add_navigation_item(change_path, "2025年", "📅")
        change_05_path = self.tree_widget.add_navigation_item(change_2025_path, "5月", "📊")
        self.tree_widget.add_navigation_item(change_05_path, "起薪", "🆕")
        self.tree_widget.add_navigation_item(change_05_path, "转正定级", "✅")
        self.tree_widget.add_navigation_item(change_05_path, "停薪-退休", "⏸️")
        self.tree_widget.add_navigation_item(change_05_path, "停薪-调离", "🔀")
        self.tree_widget.add_navigation_item(change_05_path, "考核扣款", "💸")
        
        change_04_path = self.tree_widget.add_navigation_item(change_2025_path, "4月", "📊")
        self.tree_widget.add_navigation_item(change_04_path, "起薪", "🆕")
        self.tree_widget.add_navigation_item(change_04_path, "转正定级", "✅")
        
        # 2024年异动数据（示例）
        change_2024_path = self.tree_widget.add_navigation_item(change_path, "2024年", "📅")
        change_2024_12_path = self.tree_widget.add_navigation_item(change_2024_path, "12月", "📊")
        self.tree_widget.add_navigation_item(change_2024_12_path, "起薪", "🆕")
        self.tree_widget.add_navigation_item(change_2024_12_path, "转正定级", "✅")
        
        # 注释：以下功能性导航项已移至菜单栏，不再在左侧导航中显示
        # - 数据导入（现在在"文件"菜单）
        # - 异动检测（现在在"异动"菜单）  
        # - 报告生成（现在在"报告"菜单）
        # - 系统管理（现在在"设置"菜单）
        
        # 设置默认展开和选中状态
        self.tree_widget.expand_path(salary_path, record_access=False)
        self.tree_widget.expand_path(year_2025_path, record_access=False)
        
        # 如果有数据，设置默认选中
        if active_employees_path:
            # 获取最新月份的路径进行展开
            month_path = active_employees_path.rsplit(' > ', 1)[0]  # 获取月份路径
            self.tree_widget.expand_path(month_path, record_access=False)
            self.tree_widget.select_path(active_employees_path, record_access=False)
            
        # 记录导航重构日志
        self.logger.info("导航面板已重构：移除功能性导航，专注数据导航")
    
    def _setup_connections(self):
        """
        设置连接
        """
        # 搜索连接
        self.search_edit.textChanged.connect(self._on_search_text_changed)
        self.search_debounce.search_triggered.connect(self._on_search_triggered)
        self.search_debounce.debounce_adjusted.connect(self._on_debounce_adjusted)
        
        # 树形导航连接
        self.tree_widget.itemSelectionChanged.connect(self._on_tree_selection_changed)
        self.tree_widget.smart_expansion_triggered.connect(self._on_smart_expansion)
        self.tree_widget.path_predicted.connect(self._on_path_predicted)
        
        # 定时更新统计信息 - 线程安全版本
        self._setup_stats_timer_safe()
    
    def _restore_state(self):
        """
        恢复导航状态
        """
        # 恢复展开状态
        expanded_paths = self.state_manager.get_expanded_paths()
        for path in expanded_paths:
            self.tree_widget.expand_path(path, record_access=False)
        
        # 恢复选择状态
        selected_path = self.state_manager.get_selected_path()
        if selected_path:
            self.tree_widget.select_path(selected_path, record_access=False)
        
        self.logger.info(f"恢复导航状态: {len(expanded_paths)}个展开项")
    
    def _initial_prediction(self):
        """
        初始智能预测
        """
        context = {
            'session_start': True,
            'time_of_day': 'morning'  # 可以根据实际时间确定
        }
        
        self.tree_widget.predict_and_expand(context)
    
    def _on_search_text_changed(self, text: str):
        """
        处理搜索文本变化
        
        Args:
            text: 搜索文本
        """
        # 触发智能搜索
        context = {
            'source': 'navigation_search',
            'navigation_state': 'active'
        }
        self.search_debounce.search(text, context)
        
        # 实时过滤（用于短查询的即时反馈）
        if len(text) <= 2:
            self.tree_widget.filter_items(text)
    
    def _on_search_triggered(self, query: str, context: dict):
        """
        处理搜索触发
        
        Args:
            query: 搜索查询
            context: 搜索上下文
        """
        # 执行搜索过滤
        self.tree_widget.filter_items(query)
        
        # 保存搜索历史
        if query:
            self.state_manager.add_search_history(query)
        
        # 发射搜索信号
        self.search_performed.emit(query, context)
        
        # 更新搜索状态
        if context.get('cached'):
            self.search_status.setText("💨 缓存结果")
        else:
            complexity = context.get('query_complexity', 0)
            if complexity > 0.7:
                self.search_status.setText("🧠 复杂搜索")
            else:
                self.search_status.setText("⚡ 快速搜索")
        
        self.logger.debug(f"搜索执行: '{query}', 复杂度: {context.get('query_complexity', 0):.2f}")
    
    def _on_debounce_adjusted(self, delay_ms: int):
        """
        处理防抖延迟调整
        
        Args:
            delay_ms: 延迟毫秒数
        """
        if delay_ms > 400:
            self.search_status.setText(f"⏱️ 智能延迟 {delay_ms}ms")
        elif delay_ms < 200:
            self.search_status.setText(f"⚡ 快速响应 {delay_ms}ms")
        else:
            self.search_status.setText(f"🎯 优化延迟 {delay_ms}ms")
    
    def _on_tree_selection_changed(self):
        """
        处理树形选择变化
        """
        current = self.tree_widget.currentItem()
        if current:
            path = current.data(0, Qt.UserRole)
            if path:
                # 保存选择状态
                self.state_manager.save_selected_state(path)
                
                # 触发智能预测
                context = {
                    'selected_path': path,
                    'action': 'navigation'
                }
                self.tree_widget.predict_and_expand(context)
                
                # 发射导航信号
                self.navigation_changed.emit(path, context)
                
                self.logger.info(f"导航选择: {path}")
    
    def _on_smart_expansion(self, path: str, expanded: bool):
        """
        处理智能展开事件
        
        Args:
            path: 路径
            expanded: 是否展开
        """
        # 保存展开状态
        self.state_manager.save_expanded_state(path, expanded)
        
        self.logger.debug(f"智能展开: {path} -> {expanded}")
    
    def _on_path_predicted(self, predicted_paths: List[str]):
        """
        处理路径预测事件
        
        Args:
            predicted_paths: 预测的路径列表
        """
        self.expansion_predicted.emit(predicted_paths)
        
        # 更新智能指示器
        if predicted_paths:
            self.smart_indicator.setText(f"🤖 {len(predicted_paths)}")
            self.smart_indicator.setToolTip(f"预测了 {len(predicted_paths)} 个可能的导航路径")
        else:
            self.smart_indicator.setText("🤖")
            self.smart_indicator.setToolTip("智能预测已启用")
    
    def _update_statistics(self):
        """
        更新统计信息
        """
        tree_stats = self.tree_widget.get_statistics()
        search_stats = self.search_debounce.get_statistics()
        
        stats_text = (
            f"导航项: {tree_stats['total_items']} | "
            f"搜索: {search_stats['total_queries']} | "
            f"缓存率: {search_stats['cache_hit_rate']:.1%} | "
            f"智能访问: {tree_stats['expansion_stats']['total_accesses']}"
        )
        
        self.stats_label.setText(stats_text)
    
    def handle_responsive_change(self, breakpoint: str, config: Dict[str, Any]):
        """
        处理响应式变化
        
        Args:
            breakpoint: 断点名称
            config: 布局配置
        """
        sidebar_width = config.get('sidebar_width', 280)
        sidebar_mode = config.get('sidebar_mode', 'fixed')
        
        if sidebar_mode == 'drawer':
            self.hide()
        else:
            self.show()
            self.setFixedWidth(sidebar_width)
        
        # 调整字体大小
        font_scale = config.get('font_scale', 1.0)
        if hasattr(self, 'search_edit'):
            font = self.search_edit.font()
            font.setPointSize(int(14 * font_scale))
            self.search_edit.setFont(font)
    
    def get_current_path(self) -> str:
        """
        获取当前选择的路径
        
        Returns:
            当前路径
        """
        current = self.tree_widget.currentItem()
        if current:
            return current.data(0, Qt.UserRole) or ""
        return ""
    
    def navigate_to_path(self, path: str):
        """
        导航到指定路径
        
        Args:
            path: 目标路径
        """
        self.tree_widget.select_path(path)
    
    def clear_search(self):
        """
        清空搜索
        """
        self.search_edit.clear()
        self.tree_widget.filter_items("")
        self.search_status.setText("")
    
    def get_navigation_statistics(self) -> Dict[str, Any]:
        """
        获取导航统计信息

        Returns:
            统计信息字典
        """
        return {
            'tree_stats': self.tree_widget.get_statistics(),
            'search_stats': self.search_debounce.get_statistics(),
            'state_stats': {
                'expanded_paths': len(self.state_manager.get_expanded_paths()),
                'search_history': len(self.state_manager.get_search_history())
            }
        }

    def refresh_navigation_state(self):
        """
        🔧 [P2-2] 刷新导航面板状态

        确保导航面板与系统状态保持同步
        """
        try:
            self.logger.info("🔧 [P2-2] 开始刷新导航面板状态")

            # 🔧 [P2-2] 刷新状态管理器
            if hasattr(self.state_manager, 'refresh_state'):
                self.state_manager.refresh_state()

            # 🔧 [P2-2] 刷新智能展开算法状态
            if hasattr(self.tree_widget.smart_expansion, 'refresh_predictions'):
                self.tree_widget.smart_expansion.refresh_predictions()

            # 🔧 [P2-2] 刷新搜索防抖状态
            if hasattr(self.search_debounce, 'reset_statistics'):
                self.search_debounce.reset_statistics()

            # 🔧 [P2-2] 更新统计信息
            self._update_statistics()

            # 🔧 [P2-2] 验证状态一致性
            self._verify_navigation_state_consistency()

            self.logger.info("🔧 [P2-2] 导航面板状态刷新完成")

        except Exception as e:
            self.logger.error(f"🔧 [P2-2] 刷新导航面板状态失败: {e}")

    def sync_with_global_state(self, global_state: dict):
        """
        🔧 [P2-2] 与全局状态同步

        Args:
            global_state: 全局状态字典
        """
        try:
            self.logger.info("🔧 [P2-2] 开始与全局状态同步")

            # 🔧 [P2-2] 同步当前表状态
            current_table = global_state.get('current_table')
            if current_table:
                # 尝试根据表名推断导航路径
                inferred_path = self._infer_navigation_path_from_table(current_table)
                if inferred_path and inferred_path != self.get_current_path():
                    self.logger.info(f"🔧 [P2-2] 根据全局状态同步导航路径: {inferred_path}")
                    self.navigate_to_path(inferred_path)

            # 🔧 [P2-2] 同步导航路径
            navigation_path = global_state.get('navigation_path', [])
            if navigation_path:
                path_str = " > ".join(navigation_path)
                if path_str != self.get_current_path():
                    self.logger.info(f"🔧 [P2-2] 同步导航路径: {path_str}")
                    self.navigate_to_path(path_str)

            # 🔧 [P2-2] 同步活跃表列表
            active_tables = global_state.get('active_tables', [])
            if active_tables:
                self._highlight_active_tables(active_tables)

            self.logger.debug("🔧 [P2-2] 全局状态同步完成")

        except Exception as e:
            self.logger.error(f"🔧 [P2-2] 全局状态同步失败: {e}")

    def _infer_navigation_path_from_table(self, table_name: str) -> Optional[str]:
        """
        🔧 [P2-2] 从表名推断导航路径

        Args:
            table_name: 表名

        Returns:
            推断的导航路径
        """
        try:
            # 解析表名中的信息
            if "2025" in table_name and "07" in table_name:
                if "全部在职人员" in table_name:
                    return "工资表 > 2025年 > 07月 > 全部在职人员"
                elif "离休人员" in table_name:
                    return "工资表 > 2025年 > 07月 > 离休人员"
                elif "退休人员" in table_name:
                    return "工资表 > 2025年 > 07月 > 退休人员"
                elif "A岗职工" in table_name:
                    return "工资表 > 2025年 > 07月 > A岗职工"

            # 可以添加更多推断规则
            self.logger.debug(f"🔧 [P2-2] 无法从表名推断导航路径: {table_name}")
            return None

        except Exception as e:
            self.logger.error(f"🔧 [P2-2] 推断导航路径失败: {e}")
            return None

    def _highlight_active_tables(self, active_tables: list):
        """
        🔧 [P2-2] 高亮显示活跃表

        Args:
            active_tables: 活跃表列表
        """
        try:
            # 清除之前的高亮
            for item in self.tree_widget.path_item_map.values():
                item.setBackground(0, QColor(255, 255, 255, 0))  # 透明背景

            # 高亮活跃表对应的导航项
            for table_name in active_tables:
                path = self._infer_navigation_path_from_table(table_name)
                if path and path in self.tree_widget.path_item_map:
                    item = self.tree_widget.path_item_map[path]
                    item.setBackground(0, QColor(173, 216, 230, 100))  # 浅蓝色背景

            self.logger.debug(f"🔧 [P2-2] 已高亮{len(active_tables)}个活跃表")

        except Exception as e:
            self.logger.error(f"🔧 [P2-2] 高亮活跃表失败: {e}")

    def auto_select_latest_data(self) -> bool:
        """
        自动选择最新数据并触发加载

        步骤：
        1. 获取最新数据路径
        2. 逐级展开导航树节点
        3. 选中目标节点
        4. 触发选择变化事件
        5. 自动加载对应数据

        Returns:
            bool: 是否成功选择了最新数据
        """
        try:
            self.logger.info("🔧 [P1-2修复] 开始自动选择最新数据...")

            # 🔧 [P1-2修复] 获取最新数据路径，带重试机制
            latest_path = self._get_latest_path_with_retry()
            if not latest_path:
                self.logger.warning("🔧 [P1-2修复] 多次重试后仍未找到最新工资数据路径")
                return False

            self.logger.info(f"获取到最新数据路径: {latest_path}")

            # 解析路径层级
            path_parts = [part.strip() for part in latest_path.split(' > ')]
            if len(path_parts) < 4:
                self.logger.warning(f"路径格式不正确: {latest_path}")
                return False

            # 逐级展开导航树
            current_path = ""
            for i, part in enumerate(path_parts):
                if i == 0:
                    current_path = part
                else:
                    current_path = f"{current_path} > {part}"

                # 展开当前路径（除了最后一级）
                if i < len(path_parts) - 1:
                    self.tree_widget.expand_path(current_path, record_access=False)
                    self.logger.debug(f"展开路径: {current_path}")

                    # 添加小延迟确保展开操作完成
                    QTimer.singleShot(50, lambda: None)

            # 选中最终目标节点
            self.tree_widget.select_path(latest_path, record_access=True)
            self.logger.info(f"已自动选择最新数据: {latest_path}")

            # 保存选择状态
            self.state_manager.save_selected_state(latest_path)

            # 手动触发导航变化事件，并标记为自动选择
            context = {
                'auto_selected': True,
                'selected_path': latest_path,
                'action': 'auto_navigation'
            }
            self.navigation_changed.emit(latest_path, context)

            return True

        except Exception as e:
            self.logger.error(f"自动选择最新数据失败: {e}", exc_info=True)
            return False

    def _delayed_auto_select_latest_data(self):
        """
        延迟自动选择最新数据

        在UI完全初始化后执行，避免初始化过程中的冲突
        """
        try:
            self.logger.info("执行延迟的自动选择最新数据...")

            # 检查是否已有选择的路径，如果有则不自动选择
            current_path = self.get_current_path()
            if current_path:
                self.logger.info(f"已有选择的路径: {current_path}，跳过自动选择")
                self._auto_select_pending = False
                return

            # 检查是否即将进行导航树刷新
            if hasattr(self, '_initial_load_complete') and not self._initial_load_complete:
                self.logger.info("导航树即将刷新，跳过当前自动选择，将在刷新后执行")
                return

            # 执行自动选择
            success = self.auto_select_latest_data()
            if success:
                self.logger.info("延迟自动选择最新数据成功")
                self._auto_select_pending = False
            else:
                self.logger.warning("延迟自动选择最新数据失败，可能没有可用数据")

        except Exception as e:
            self.logger.error(f"延迟自动选择最新数据出错: {e}", exc_info=True)
    
    def _get_latest_path_with_retry(self, max_retries: int = 3) -> Optional[str]:
        """
        🔧 [P1-2修复] 带重试机制的获取最新数据路径
        
        Args:
            max_retries: 最大重试次数
            
        Returns:
            Optional[str]: 最新数据路径，失败返回None
        """
        import time
        
        for attempt in range(max_retries + 1):
            try:
                # 🔧 [P1-2修复] 先检查数据库连接状态
                if hasattr(self.dynamic_table_manager, 'is_database_ready'):
                    if not self.dynamic_table_manager.is_database_ready():
                        if attempt < max_retries:
                            delay = 1.0  # 数据库未就绪时等待更长时间
                            self.logger.info(f"🔧 [P1-2修复] 数据库未就绪，{delay}s后重试...")
                            time.sleep(delay)
                            continue
                        else:
                            self.logger.warning(f"🔧 [P1-2修复] 数据库连接超时")
                            return None
                
                # 🔧 [P1-2修复] 检查表管理器状态
                if not self.dynamic_table_manager:
                    self.logger.warning(f"🔧 [P1-2修复] DynamicTableManager不可用")
                    return None
                
                # 获取最新数据路径
                latest_path = self.dynamic_table_manager.get_latest_salary_data_path()
                if latest_path:
                    self.logger.info(f"🔧 [P1-2修复] 第{attempt + 1}次尝试成功获取到路径: {latest_path}")
                    return latest_path
                    
                if attempt < max_retries:
                    # 🔧 [P1-2修复] 优化重试策略：检查具体失败原因
                    try:
                        # 尝试检查是否有任何表存在
                        tables = self.dynamic_table_manager.get_all_table_names()
                        if not tables:
                            delay = (attempt + 1) * 1.0  # 无表时延迟更长：1s, 2s, 3s
                            self.logger.info(f"🔧 [P1-2修复] 数据库中暂无表，{delay}s后重试...")
                        else:
                            delay = (attempt + 1) * 0.5  # 有表但无最新路径：0.5s, 1s, 1.5s
                            self.logger.info(f"🔧 [P1-2修复] 找到{len(tables)}个表但无最新路径，{delay}s后重试...")
                    except Exception:
                        delay = 1.0
                        self.logger.info(f"🔧 [P1-2修复] 检查表状态失败，{delay}s后重试...")
                    
                    time.sleep(delay)
                    
            except Exception as e:
                self.logger.warning(f"🔧 [P1-2修复] 第{attempt + 1}次尝试出错: {e}")
                if attempt < max_retries:
                    delay = 1.0  # 异常时固定延迟1秒
                    time.sleep(delay)
                    
        self.logger.warning(f"🔧 [P1-2修复] {max_retries + 1}次尝试均失败，可能数据导入尚未完成")
        return None
    
    def _load_dynamic_salary_data(self, year_path: str) -> Optional[str]:
        """
        动态加载工资数据导航项

        从元数据中读取结构化的工资数据表信息，并创建对应的导航项。
        现在包含启动延迟机制，确保数据库完全就绪后再加载。
        
        Args:
            year_path: 年份节点的路径
            
        Returns:
            最新月份活跃员工的路径，用于默认选择
        """
        try:
            self.logger.info("开始从元数据动态加载工资数据...")
            
            # 检查是否需要延迟加载（首次启动时）
            if not hasattr(self, '_initial_load_complete'):
                self._initial_load_complete = False
                
            if not self._initial_load_complete:
                self.logger.info("检测到首次启动，将延迟加载导航数据确保数据库就绪")
                # 使用QTimer延迟加载，给数据库同步留出时间
                QTimer.singleShot(800, lambda: self._delayed_load_salary_data(year_path))
                return None
            
            return self._execute_salary_data_load(year_path)
            
        except Exception as e:
            self.logger.error(f"动态加载工资数据失败: {e}", exc_info=True)
            # 如果动态加载失败，返回静态数据作为兜底
            return self._load_fallback_salary_data(year_path)

    def _delayed_load_salary_data(self, year_path: str):
        """延迟加载工资数据（用于首次启动）"""
        try:
            self.logger.info("执行延迟的工资数据加载...")
            self._initial_load_complete = True

            # 首先强制刷新工资数据以确保获取最新数据
            self.force_refresh_salary_data()

            # 导航树刷新完成后，重新执行自动选择和展开
            QTimer.singleShot(200, self._post_refresh_auto_select)

        except Exception as e:
            self.logger.error(f"延迟加载工资数据失败: {e}")
            # 失败时使用兜底数据
            try:
                self._load_fallback_salary_data(year_path)
            except Exception as fallback_error:
                self.logger.error(f"兜底数据加载也失败: {fallback_error}")

    def _post_refresh_auto_select(self):
        """
        导航树刷新后重新执行自动选择

        确保在导航树完全重建后，重新选择和展开最新数据路径
        """
        try:
            # 检查是否需要执行自动选择
            if not getattr(self, '_auto_select_pending', False):
                self.logger.info("无需执行自动选择，跳过")
                return

            self.logger.info("导航树刷新完成，重新执行自动选择...")

            # 获取最新数据路径
            latest_path = self.dynamic_table_manager.get_latest_salary_data_path()
            if not latest_path:
                self.logger.warning("未找到最新工资数据路径")
                self._auto_select_pending = False
                return

            self.logger.info(f"重新选择最新数据路径: {latest_path}")

            # 解析路径层级
            path_parts = [part.strip() for part in latest_path.split(' > ')]
            if len(path_parts) < 4:
                self.logger.warning(f"路径格式不正确: {latest_path}")
                self._auto_select_pending = False
                return

            # 逐级展开导航树（重要：确保所有父级节点都展开）
            current_path = ""
            for i, part in enumerate(path_parts):
                if i == 0:
                    current_path = part
                else:
                    current_path = f"{current_path} > {part}"

                # 展开当前路径（包括所有父级）
                if i < len(path_parts) - 1:
                    self.tree_widget.expand_path(current_path, record_access=False)
                    self.logger.debug(f"重新展开路径: {current_path}")

            # 选中最终目标节点
            self.tree_widget.select_path(latest_path, record_access=True)
            self.logger.info(f"重新选择完成: {latest_path}")

            # 保存选择状态
            self.state_manager.save_selected_state(latest_path)

            # 手动触发导航变化事件（因为树重建后可能不会自动触发）
            context = {
                'auto_selected': True,
                'selected_path': latest_path,
                'action': 'post_refresh_auto_navigation'
            }
            self.navigation_changed.emit(latest_path, context)

            # 标记自动选择完成
            self._auto_select_pending = False

        except Exception as e:
            self.logger.error(f"导航树刷新后自动选择失败: {e}", exc_info=True)
            self._auto_select_pending = False

    def _execute_salary_data_load(self, year_path: str) -> Optional[str]:
        """执行实际的工资数据加载逻辑"""
        navigation_data = self.dynamic_table_manager.get_navigation_tree_data()
        
        if not navigation_data:
            self.logger.warning("未从元数据中获取到任何导航数据，使用兜底数据")
            return self._load_fallback_salary_data(year_path)

        latest_active_path = None
        
        # 直接遍历结构化数据构建UI
        for year, months_data in navigation_data.items():
            # 注意：目前只处理一个年份节点，如果未来需要支持多年份，这里的逻辑需要调整
            if str(year) in year_path: 
                for month, items in months_data.items():
                    month_path = self.tree_widget.add_navigation_item(year_path, f"{month}月", "📊")
                    
                    for item_info in items:
                        employee_path = self.tree_widget.add_navigation_item(
                            month_path, 
                            item_info['display_name'], 
                            item_info['icon']
                        )
                        
                        # 记录最新月份的活跃员工路径
                        if latest_active_path is None and item_info['display_name'] == '全部在职人员':
                            latest_active_path = employee_path

        self.logger.info(f"动态加载了 {len(navigation_data.get(2025, {}))} 个月份的工资数据导航")
        return latest_active_path

    def _load_fallback_salary_data(self, year_path: str) -> Optional[str]:
        """加载兜底的工资数据（静态数据）"""
        try:
            # 5月数据（兜底）
            month_05_path = self.tree_widget.add_navigation_item(year_path, "5月", "📊")
            active_employees_path = self.tree_widget.add_navigation_item(month_05_path, "全部在职人员", "👥")
            self.tree_widget.add_navigation_item(month_05_path, "离休人员", "🏆")
            self.tree_widget.add_navigation_item(month_05_path, "退休人员", "🌅")
            self.tree_widget.add_navigation_item(month_05_path, "A岗职工", "⭐")
            
            self.logger.warning("使用兜底数据加载导航")
            return active_employees_path
            
        except Exception as e:
            self.logger.error(f"加载兜底数据失败: {e}")
            return None
    
    def refresh_navigation_data(self):
        """
        🔧 [P2-2] 增强版刷新导航数据，包含完整的状态管理
        """
        try:
            self.logger.info("🔧 [P2-2] 开始增强版导航数据刷新...")

            # 🔧 [P2-2] 保存完整的导航状态
            navigation_state = self._capture_navigation_state()

            # 清空树形控件
            self.tree_widget.clear()

            # 重新加载导航数据
            self._setup_navigation_data()

            # 🔧 [P2-2] 使用增强的状态恢复机制
            self._restore_navigation_state(navigation_state)

            # 🔧 [P2-2] 验证状态恢复结果
            self._verify_navigation_state_consistency()

            self.logger.info("🔧 [P2-2] 增强版导航数据刷新完成")
        except Exception as e:
            self.logger.error(f"🔧 [P2-2] 刷新导航数据失败: {e}")

    def _capture_navigation_state(self) -> dict:
        """
        🔧 [P2-2] 捕获完整的导航状态

        Returns:
            导航状态字典
        """
        try:
            state = {
                'current_path': self.get_current_path(),
                'expanded_paths': list(self.state_manager.get_expanded_paths()),
                'selected_path': self.state_manager.get_selected_path(),
                'search_text': self.search_edit.text() if hasattr(self, 'search_edit') else "",
                'tree_scroll_position': None,
                'timestamp': time.time()
            }

            # 捕获树形控件的滚动位置
            if hasattr(self.tree_widget, 'verticalScrollBar'):
                scroll_bar = self.tree_widget.verticalScrollBar()
                if scroll_bar:
                    state['tree_scroll_position'] = scroll_bar.value()

            self.logger.debug(f"🔧 [P2-2] 导航状态已捕获: {len(state['expanded_paths'])}个展开项")
            return state

        except Exception as e:
            self.logger.error(f"🔧 [P2-2] 捕获导航状态失败: {e}")
            return {}

    def _restore_navigation_state(self, state: dict):
        """
        🔧 [P2-2] 恢复导航状态

        Args:
            state: 导航状态字典
        """
        try:
            if not state:
                self.logger.warning("🔧 [P2-2] 导航状态为空，使用默认恢复机制")
                self._restore_state()
                return

            self.logger.info("🔧 [P2-2] 开始恢复导航状态...")

            # 🔧 [P2-2] 恢复展开状态
            expanded_paths = state.get('expanded_paths', [])
            for path in expanded_paths:
                try:
                    self.tree_widget.expand_path(path, record_access=False)
                except Exception as e:
                    self.logger.warning(f"🔧 [P2-2] 恢复展开路径失败: {path} - {e}")

            # 🔧 [P2-2] 恢复选择状态
            current_path = state.get('current_path') or state.get('selected_path')
            if current_path:
                try:
                    self.navigate_to_path(current_path)
                except Exception as e:
                    self.logger.warning(f"🔧 [P2-2] 恢复选择状态失败: {current_path} - {e}")

            # 🔧 [P2-2] 恢复搜索状态
            search_text = state.get('search_text', "")
            if search_text and hasattr(self, 'search_edit'):
                self.search_edit.setText(search_text)
                self.tree_widget.filter_items(search_text)

            # 🔧 [P2-2] 恢复滚动位置
            scroll_position = state.get('tree_scroll_position')
            if scroll_position is not None and hasattr(self.tree_widget, 'verticalScrollBar'):
                scroll_bar = self.tree_widget.verticalScrollBar()
                if scroll_bar:
                    QTimer.singleShot(100, lambda: scroll_bar.setValue(scroll_position))

            self.logger.info(f"🔧 [P2-2] 导航状态恢复完成: {len(expanded_paths)}个展开项")

        except Exception as e:
            self.logger.error(f"🔧 [P2-2] 恢复导航状态失败: {e}")

    def _verify_navigation_state_consistency(self):
        """
        🔧 [P2-2] 验证导航状态一致性
        """
        try:
            issues = []

            # 检查当前选择是否有效
            current_path = self.get_current_path()
            if current_path:
                current_item = self.tree_widget.currentItem()
                if not current_item:
                    issues.append(f"选择路径存在但无对应项目: {current_path}")

            # 检查展开状态一致性
            expanded_paths = self.state_manager.get_expanded_paths()
            for path in expanded_paths:
                if path in self.tree_widget.path_item_map:
                    item = self.tree_widget.path_item_map[path]
                    if not item.isExpanded():
                        issues.append(f"路径应展开但未展开: {path}")

            # 检查树形控件映射完整性
            if len(self.tree_widget.item_path_map) != len(self.tree_widget.path_item_map):
                issues.append("树形控件路径映射不一致")

            # 报告问题
            if issues:
                self.logger.warning(f"🔧 [P2-2] 导航状态一致性问题: {issues}")
                return False
            else:
                self.logger.debug("🔧 [P2-2] 导航状态一致性验证通过")
                return True

        except Exception as e:
            self.logger.error(f"🔧 [P2-2] 导航状态一致性验证失败: {e}")
            return False
            
    def force_refresh_salary_data(self):
        """
        强制刷新工资数据导航
        现在包含重试机制和更好的错误处理
        """
        max_retries = 2
        for retry_count in range(max_retries + 1):
            try:
                self.logger.info(f"开始强制刷新工资数据导航... (尝试 {retry_count + 1}/{max_retries + 1})")
                
                # 找到工资表节点
                salary_item = None
                for i in range(self.tree_widget.topLevelItemCount()):
                    item = self.tree_widget.topLevelItem(i)
                    if item and "工资表" in item.text(0):
                        salary_item = item
                        self.logger.info(f"找到工资表节点: {item.text(0)}")
                        break
                
                if not salary_item:
                    self.logger.error("未找到工资表节点，无法刷新")
                    return
                
                # 清除工资表下的所有子节点，并清理相应的映射
                def remove_child_mappings(item):
                    """递归清理子节点的映射"""
                    if item and id(item) in self.tree_widget.item_path_map:
                        path = self.tree_widget.item_path_map[id(item)]
                        # 从映射中移除
                        self.tree_widget.path_item_map.pop(path, None)
                        self.tree_widget.item_path_map.pop(id(item), None)
                    
                    # 递归处理子节点
                    for i in range(item.childCount() if item else 0):
                        child = item.child(i)
                        remove_child_mappings(child)
                
                # 清除工资表下的所有子节点
                while salary_item.childCount() > 0:
                    removed_child = salary_item.child(0)
                    self.logger.debug(f"移除子节点: {removed_child.text(0) if removed_child else 'None'}")
                    # 清理子节点及其后代的映射
                    remove_child_mappings(removed_child)
                    salary_item.removeChild(removed_child)
                
                # 确保工资表节点本身有正确的路径数据
                salary_item.setData(0, Qt.UserRole, "工资表")
                self.tree_widget.item_path_map[id(salary_item)] = "工资表"
                self.tree_widget.path_item_map["工资表"] = salary_item
                
                self.logger.debug("已清理工资表子节点的项目映射并重设工资表路径")
                
                # 通知智能展开算法树形结构已重建，清除相关缓存
                if hasattr(self.tree_widget, 'smart_expansion'):
                    self.tree_widget.smart_expansion.clear_path_cache()
                    self.logger.debug("已清理智能展开算法的路径缓存")
                
                # 获取动态导航数据（带重试机制）
                navigation_data = self.dynamic_table_manager.get_navigation_tree_data()
                
                if not navigation_data:
                    # 如果是第一次尝试且数据库确实为空，直接使用fallback数据，不需要重试
                    if retry_count == 0:
                        # 检查是否是数据库为空的情况
                        all_tables = self.dynamic_table_manager.get_table_list()
                        salary_tables = [t for t in all_tables if t.get('table_name', '').startswith('salary_data_')]
                        
                        if len(salary_tables) == 0:
                            self.logger.warning("检测到数据库中没有工资数据表，直接使用兜底数据")
                            salary_path = "工资表"
                            year_2025_path = self.tree_widget.add_navigation_item(salary_path, "2025年", "📅")
                            self._load_fallback_salary_data(year_2025_path)
                            return
                    
                    if retry_count < max_retries:
                        self.logger.warning(f"未获取到导航数据，将在500ms后重试 (尝试 {retry_count + 1}/{max_retries + 1})")
                        # 避免无限重试 - 使用一个实例变量来跟踪重试状态
                        if not hasattr(self, '_force_refresh_retrying'):
                            self._force_refresh_retrying = True
                            QTimer.singleShot(500, lambda: self._continue_force_refresh(retry_count))
                        return
                    else:
                        self.logger.warning("多次尝试后仍未从元数据中获取到任何导航数据，使用兜底数据")
                        # 使用原有的静态结构作为兜底
                        salary_path = "工资表"
                        year_2025_path = self.tree_widget.add_navigation_item(salary_path, "2025年", "📅")
                        self._load_fallback_salary_data(year_2025_path)
                        if hasattr(self, '_force_refresh_retrying'):
                            delattr(self, '_force_refresh_retrying')
                        return
                
                latest_active_path = None
                year_paths = {}
                
                # 根据动态数据重建所有年份结构
                for year, months_data in sorted(navigation_data.items(), reverse=True):  # 年份倒序，最新年份在前
                    # 使用确定的工资表路径
                    salary_path = "工资表"
                    year_path = self.tree_widget.add_navigation_item(salary_path, f"{year}年", "📅")
                    year_paths[year] = year_path
                    
                    self.logger.info(f"创建年份节点: {year}年，包含 {len(months_data)} 个月份")
                    
                    # 添加该年份下的所有月份
                    for month, items in sorted(months_data.items()):
                        month_path = self.tree_widget.add_navigation_item(year_path, f"{month}月", "📊")
                        
                        for item_info in items:
                            employee_path = self.tree_widget.add_navigation_item(
                                month_path, 
                                item_info['display_name'], 
                                item_info['icon']
                            )
                            
                            # 记录最新年份最大月份的活跃员工路径作为默认选择
                            if latest_active_path is None and item_info['display_name'] == '全部在职人员':
                                latest_active_path = employee_path
                
                # 统计信息
                total_years = len(navigation_data)
                total_months = sum(len(months) for months in navigation_data.values())
                self.logger.info(f"工资数据导航已强制刷新: {total_years} 个年份, {total_months} 个月份")
                
                if latest_active_path:
                    self.logger.info(f"默认路径: {latest_active_path}")
                else:
                    self.logger.warning("未找到默认选择路径")
                
                # 刷新成功，跳出重试循环
                break
                
            except Exception as e:
                if retry_count < max_retries:
                    self.logger.warning(f"强制刷新工资数据导航失败 (尝试 {retry_count + 1}/{max_retries + 1}): {e}")
                    # 等待递增的时间后重试
                    retry_delay = (retry_count + 1) * 300  # 300ms, 600ms
                    QTimer.singleShot(retry_delay, lambda: self.force_refresh_salary_data())
                    return
                else:
                    self.logger.error(f"强制刷新工资数据导航最终失败: {e}")
                    import traceback
                    self.logger.error(f"详细错误信息: {traceback.format_exc()}")
                    
                    # 最后的兜底策略：尝试加载静态数据
                    try:
                        salary_item = None
                        for i in range(self.tree_widget.topLevelItemCount()):
                            item = self.tree_widget.topLevelItem(i)
                            if item and "工资表" in item.text(0):
                                salary_item = item
                                break
                        
                        if salary_item:
                            salary_path = "工资表"
                            year_2025_path = self.tree_widget.add_navigation_item(salary_path, "2025年", "📅")
                            self._load_fallback_salary_data(year_2025_path)
                            self.logger.info("已使用兜底数据恢复基本导航结构")
                    except Exception as fallback_error:
                        self.logger.error(f"兜底数据加载也失败: {fallback_error}")
                        
        # 确保重试循环结束后停止进一步的重试调用
        if hasattr(self, '_force_refresh_retrying'):
            delattr(self, '_force_refresh_retrying')
        self.logger.info("force_refresh_salary_data 执行完成")
        
    def _continue_force_refresh(self, current_retry_count: int):
        """继续重试强制刷新，避免重复调用"""
        if hasattr(self, '_force_refresh_retrying'):
            delattr(self, '_force_refresh_retrying')
            # 继续下一次重试
            self.force_refresh_salary_data() 